@import '../../../assets/scss/main.scss';

.actionBtns {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 0.5em;
  @include for_media(mobileScreen) {
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
    gap: 0.7em;
    flex-direction: column;
    justify-content: center;
  }

  /* For business drodown */
  :global(.menu) {
    border: none !important;
    box-shadow:
      0 10px 15px -3px rgb(0 0 0 / 0.1),
      0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
  }
  :global(.search) {
    height: 100% !important;
    width: auto !important;
    display: flex !important;
    align-items: center !important;
    background-color: $accentBgColor1 !important;
    border: 2px solid $accentBorder1 !important;
    border-radius: 1em !important;
    color: $primaryColor !important;
    font-weight: normal !important;
    transition: all 0.2s ease !important;
    box-shadow: none !important;
    cursor: pointer;
  }
  :global(.default) {
    color: #0b1a3073 !important;
  }
}

.tableContainer {
  width: 100%;
  overflow-x: scroll;
  margin-top: 1.5em;
  @include hide-scrollbar;
  @include for_media(mobileScreen) {
    max-height: 60vh;
    max-height: 60dvh;
    overflow-y: auto;
    padding-bottom: 5em;
  }
}

.paginationWrapper {
  display: flex;
  width: 100%;
  height: fit-content;
  @include for_media(mobileScreen) {
    justify-content: center;
    position: fixed;
    bottom: 10%;
    padding-left: 10em;
    padding-bottom: 1em;
    left: 0;
    z-index: 100;
    max-width: 90vw;
    overflow-x: auto;
  }
}

.markAsCompletedBtn {
  display: flex !important;
  align-items: center !important;
  gap: 0.5em;
  flex-shrink: 0 !important;
  background-color: #eaecf5 !important;
  border: 2px solid #9ea5d1 !important;
  border-radius: 0.7em !important;
  color: #333 !important;
  padding: 0.6em !important;
  font-weight: normal !important;
  transition: all 0.2s ease !important;
  box-shadow: none !important;
  width: fit-content !important;
  cursor: pointer;

  &:hover {
    background-color: #e9ecef !important;
    border-color: #ced4da !important;
  }
}

.searchWrapper {
  background-color: $accentBgColor1 !important;
  border: 2px solid $accentBorder1 !important;
  @include for_media(mobileScreen) {
    width: 60%;
  }
  .searchInputField {
    width: 100% !important;
    min-height: 2.5em !important;
    background-color: #4ac3ff00 !important;
    color: $black !important;
    box-shadow: none !important;
  }
}
