import React, { createContext, useContext, useEffect, useMemo, useRef, useState } from 'react';
import ls from 'local-storage';
import { useNavigate, useLocation } from 'react-router-dom';
import { domain } from '../components/utils/constants';
import userServices, { getBusinesses } from '../components/services/userServices';
import { decryptData } from '../components/utils/cryptoUtils';
import { mediaBreakpoint } from '../components/global/MediaBreakPointes';

const getRoleType = (role) => {
  if (role === 'manager' || role === 'accountant') return 'accountant';
  if (role === 'superuser') return 'admin';
  return 'user';
};

const selectedBusiness = ls.get('globSelectedBusiness') || null;

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const decryptRole = useMemo(() => {
    const encryptedRole = ls.get('access_token')?.role;
    return encryptedRole ? decryptData(encryptedRole) : 'normal';
  }, []);
  const [role, setRole] = useState(decryptRole);
  const [roleType, setRoleType] = useState(getRoleType(decryptRole));
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userInfo, setUserInfo] = useState({});
  const [headerLogo, setLogo] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [businessList, setBusiness] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [hasNewNotification, setHasNewNotification] = useState(false);
  const isMobileScreen = useRef(mediaBreakpoint?.mobile > window.innerWidth);
  const [userProfile, setUserProfile] = useState();
  const [globSelectedBusiness, setGlobSelectedBusiness] = useState(selectedBusiness);
  const navigate = useNavigate();
  const location = useLocation();

  const handleTokenInvalidation = () => {
    ls.remove('access_token');
    setIsAuthenticated(false);
    if (location.pathname !== '/login') navigate('/login');
  };

  const token = ls.get('access_token')?.data;
  useEffect(() => {
    const token = ls.get('access_token')?.data;
    if (!token) {
      console.error('Access token is missing.');
      return;
    }

    const wsUrl = `wss://${domain}/ws/notifications/?token=${token}`;

    const connectWebSocket = () => {
      const socket = new WebSocket(wsUrl);

      socket.onopen = () => {
        console.log('WebSocket connected.');
      };

      socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (Array.isArray(data.notifications)) {
            setNotifications((prevNotifications) => [...data.notifications, ...prevNotifications]);
          } else if (data.notifications) {
            setNotifications((prevNotifications) => [data.notifications, ...prevNotifications]);
            setHasNewNotification(true);
          } else {
            setNotifications((prevNotifications) => [data, ...prevNotifications]);
            setHasNewNotification(true);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      socket.onerror = (error) => {
        console.error('WebSocket error:', error.message);
      };

      socket.onclose = (event) => {
        console.log(`WebSocket closed: ${event.reason || 'Connection closed.'}`);
        // Attempt to reconnect if the connection is closed
        setTimeout(connectWebSocket, 5000);
      }; // Reconnect after 5 seconds

      return socket;
    };

    // Initial WebSocket connection
    const socket = connectWebSocket();

    // Cleanup on component unmount
    return () => {
      console.log('Closing WebSocket connection.');
      socket.close();
    };
  }, [token]);

  const checkTokenExpiry = (call) => {
    setIsLoading(true);
    const now = new Date();
    const cookieObj = ls.get('access_token');

    if (cookieObj) {
      if (call) {
        if (userServices.isTokenValid(cookieObj?.data)) {
          setIsAuthenticated(true);
        } else {
          handleTokenInvalidation();
        }
      } else {
        if (now.getTime() <= cookieObj.expiry) {
          setIsAuthenticated(true);
          setUserInfo(cookieObj);
        } else {
          ls.remove('access_token');
          setIsAuthenticated(false);
          if (location.pathname !== '/login') navigate('/login');
        }
      }
    } else {
      setIsAuthenticated(false);
      if (location.pathname !== '/login') navigate('/login');
    }
    setIsLoading(false);
  };

  useEffect(() => {
    if (isAuthenticated) {
      setIsLoading(true);
      userServices.getProfile(role, userInfo).then((data) => {
        setUserProfile(data?.results?.[0] || null);
      });
      setIsLoading(false);
      if (!selectedBusiness) {
        getBusinesses().then((data) => {
          const business = data?.results?.[0];
          ls.set('globSelectedBusiness', business);
          setGlobSelectedBusiness(business);
        });
      }
    }
    // eslint-disable-next-line
  }, [isAuthenticated]);

  useEffect(() => {
    checkTokenExpiry();
    const interval = setInterval(() => {
      checkTokenExpiry(true);
    }, 3600000);

    return () => clearInterval(interval);
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    setRoleType(getRoleType(role));
  }, [role]);

  return (
    <AuthContext.Provider
      value={{
        role,
        notifications,
        setRole,
        roleType,
        setBusiness,
        businessList,
        isAuthenticated,
        setIsAuthenticated,
        setLogo,
        headerLogo,
        isLoading,
        userInfo,
        setUserInfo,
        setHasNewNotification,
        hasNewNotification,
        setNotifications,
        isMobileScreen: isMobileScreen.current,
        userProfile,
        globSelectedBusiness,
        setGlobSelectedBusiness,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
