@import '../../../../assets/scss/main.scss';

.invoiceTable {
  border-spacing: 0 0.7em !important;

  tr {
    td:first-child {
      border-top-left-radius: 10px;
      border-bottom-left-radius: 10px;
      padding-left: 1em !important;
    }
    td:last-child {
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;
      padding-right: 1em !important;
    }
  }

  .tableHeaderRow {
    position: sticky;
    top: 0;
    background-color: $white;
    z-index: 2;
    th {
      color: #717680 !important;
      padding: 0.3em 1em !important;
      font-weight: 600;
      white-space: nowrap;
    }
  }

  .tableRow {
    @include clickable;
    td {
      padding: 0.37em 1em !important;
      border: none !important;
      background-color: $white !important;
      vertical-align: middle;
      white-space: nowrap;
      p {
        font-size: 1em;
        font-weight: 900;
        margin: 0;
        line-height: 1.4;
      }
    }

    .fileNameWrapper {
      height: 2.5em;
      display: flex;
      gap: 0.3em;
      align-items: center;
      min-width: 200px;
    }

    &.disabledRow {
      cursor: not-allowed;
      td {
        background-color: #fcfbfb !important;
      }
      .stickyCellLeft,
      .stickyCellRight {
        background-color: #fbfbfb !important;
      }
    }
  }
}

.messageWrapper {
  display: flex;
  gap: 0.8em;
  align-items: center;
  justify-content: space-between;
  width: 85%;
  cursor: pointer;
  padding: 0.3em 0;

  .msgWrapper {
    display: flex;
    align-items: center;
    gap: 0.8em;

    svg {
      width: 25px;
      height: 25px;
      flex-shrink: 0;
    }

    .count {
      width: 22px;
      height: 22px;
      background-color: $redColor1;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 100%;
      color: $white;
      font-weight: 900;
      font-size: 0.9em;
    }
  }

  .arrowWrapper {
    svg {
      width: 25px;
      height: 25px;
      flex-shrink: 0;
    }
  }

  &:hover {
    opacity: 0.9;
  }
}

.stickyCellLeft {
  position: sticky !important;
  left: -0.5em !important;
  background-color: $white !important;
  z-index: 1;
}

.stickyCellRight {
  position: sticky !important;
  right: 0 !important;
  background-color: $white !important;
  z-index: 1;

  &::after {
    content: '';
    position: absolute;
    left: -10px;
    top: 0;
    height: 100%;
    width: 10px;
    background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.07));
  }
}

.status {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #d5d5d569;
  white-space: nowrap;
  margin: 0;
  width: 7em;
  height: 2em;
  border-radius: 35px;
  @include clickable;
}

.tooltip {
  background-color: #011638;
  border-radius: 15px;
  z-index: 10;
}
