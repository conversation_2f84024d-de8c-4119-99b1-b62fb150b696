import React from 'react';
import style from './layoutWrapper.module.scss';
import Header from '../global/Header';
import NavigationBar from '../screens/NavigationBar';
import { useAuth } from '../../contexts/AuthContext';
import Sidebar from '../layout/Sidebar';

function LayoutWrapper({ children, className }) {
  const { isMobileScreen } = useAuth();
  return (
    <Sidebar />
  );
}
export default LayoutWrapper;
