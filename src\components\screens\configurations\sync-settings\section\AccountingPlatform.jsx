import { Ch<PERSON><PERSON>Down, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Shield } from 'lucide-react';
import React, { useCallback, useState } from 'react';
import {
  fullSync,
  getAccountingPlatforms,
  getSyncStatus,
  updateBusinessPreferences,
} from '../../../../services/syncSettingsServices';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../../../../contexts/AuthContext';
import { toast } from 'react-toastify';
import { getErrorMessage } from '../../../../utils/apiUtils';
import useUpdateEffect from '../../../../global/hooks/useUpdateEffect';

const ZohoSyncButton = ({ isConnected, refetchSyncStatus, syncProgress, didSyncFailed }) => {
  const { globSelectedBusiness } = useAuth();
  const [isProgressPolling, setIsProgressPolling] = useState(syncProgress !== 100);
  const [syncFailed, setSyncFailed] = useState(didSyncFailed);
  const { data: syncStatus } = useQuery({
    queryKey: ['sync-status', globSelectedBusiness?.business_id],
    queryFn: () => getSyncStatus(globSelectedBusiness?.business_id),
    enabled: isProgressPolling && !syncFailed,
    refetchInterval: 2000,
    refetchIntervalInBackground: false,
  });

  const handleSyncNow = () => {
    setIsProgressPolling(true);
    setSyncFailed(false);
    fullSync(globSelectedBusiness?.business_id)
      .then((res) => {
        if (res?.status?.toLowerCase() !== 'success') {
          throw new Error(res?.message);
        }
      })
      .catch((error) => {
        const errorMessage = getErrorMessage(error);
        toast.error(errorMessage);
        setSyncFailed(true);
      });
  };

  // Check if sync is completed
  useUpdateEffect(() => {
    const status = syncStatus?.sync_status?.toLowerCase();
    const progress = Number(syncStatus?.sync_progress);

    if (status === 'failed') {
      setSyncFailed(true);
      return;
    }
    if (progress === 100) {
      setIsProgressPolling(false);
      if (status === 'completed') {
        refetchSyncStatus();
        toast.success('Sync completed successfully!');
      }
    }
  }, [syncStatus]);

  return (
    <div className="flex flex-col gap-3">
      {syncFailed ? (
        <div className="flex flex-col gap-1">
          {isConnected && <p className="text-sm text-error text-center m-0">Sync failed.</p>}
          <button
            onClick={handleSyncNow}
            disabled={!isConnected}
            className={`flex items-center justify-center p-2.5 rounded-xl transition-colors duration-200 whitespace-nowrap select-none gap-2 ${
              !isConnected ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-red-500 hover:bg-red-600 text-white'
            }`}
          >
            <RefreshCw className="w-5 h-5" />
            Retry Sync
          </button>
        </div>
      ) : (
        <button
          onClick={handleSyncNow}
          disabled={!isConnected || isProgressPolling}
          className="flex items-center justify-center p-2.5 rounded-xl transition-colors duration-200 whitespace-nowrap select-none common-btn-schema gap-2"
        >
          {isProgressPolling ? <Loader2 className="w-5 h-5 animate-spin" /> : <RefreshCw className="w-5 h-5" />}
          {isProgressPolling ? `Syncing... ${syncStatus?.sync_progress || 0}%` : 'Sync Now'}
        </button>
      )}

      {/* Progress Bar */}
      {isProgressPolling && (
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`bg-accent1 h-2 rounded-full transition-all duration-300 ease-out ${
              syncStatus?.sync_status?.toLowerCase() === 'failed' ? 'bg-error' : ''
            }`}
            style={{ width: `${syncStatus?.sync_progress || 0}%` }}
          />
        </div>
      )}
    </div>
  );
};

function AccountingPlatform({
  selectedPlatform,
  setSelectedPlatform,
  isConnected,
  refetchSyncStatus,
  syncProgress,
  didSyncFailed,
}) {
  const { globSelectedBusiness } = useAuth();
  const { data: platforms, isLoading } = useQuery({
    queryKey: ['accounting-platforms'],
    queryFn: () => getAccountingPlatforms(),
    staleTime: 1000 * 60 * 5,
  });

  const handleChangeAccountingPlatform = useCallback(
    (e) => {
      const targetElement = e.target;
      const selectedPlatformId = targetElement.value;
      const selectedPlatformName = targetElement.options[targetElement.selectedIndex].text;
      updateBusinessPreferences(globSelectedBusiness?.business_id, {
        platform: selectedPlatformId,
      })
        .then(() => {
          setSelectedPlatform({
            id: selectedPlatformId,
            platform_name: selectedPlatformName,
          });
          toast.success(`Accounting platform changed to ${selectedPlatformName}`);
        })
        .catch((error) => {
          toast.error(getErrorMessage(error));
        });
    },
    [globSelectedBusiness?.business_id]
  );

  return (
    <div className="mb-8 p-6 rounded-2xl shadow-lg bg-white border border-accent1-border">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-4">
            <div className="flex items-center justify-center w-6 h-6">
              <Shield className="w-5 h-5 text-accent1" />
            </div>
            <label className="text-xl font-bold leading-none text-primary-color">Accounting Platform</label>
          </div>
          <div className="relative w-fit ml-3">
            <select
              value={selectedPlatform?.id || ''}
              onChange={handleChangeAccountingPlatform}
              className="w-64 text-lg font-medium px-3 py-2 pr-12 rounded-xl border-2 focus:outline-none appearance-none hover:shadow-md text-gray-900 bg-accent1-bg border-accent1-border"
              disabled={isLoading}
            >
              {isLoading ? (
                <option value="" disabled>
                  Loading...
                </option>
              ) : (
                <>
                  <option value="" disabled>
                    Select Platform
                  </option>
                  {Array.isArray(platforms?.results) &&
                    platforms?.results?.length > 0 &&
                    platforms?.results?.map((platform) => (
                      <option key={platform.id} value={platform.id}>
                        {platform.platform_name}
                      </option>
                    ))}
                </>
              )}
            </select>
            <ChevronDown className="absolute right-4 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none text-accent1" />
          </div>
        </div>
        {selectedPlatform?.platform_name?.toLowerCase() === 'zoho' && (
          <div className="ml-6">
            <ZohoSyncButton
              isConnected={isConnected}
              refetchSyncStatus={refetchSyncStatus}
              syncProgress={syncProgress}
              didSyncFailed={didSyncFailed}
            />
          </div>
        )}
      </div>
    </div>
  );
}

export default AccountingPlatform;
