import React from 'react';
import { CloudUpload, Description, Build, Settings, Business } from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import omniSageLetterLogo from '../../assets/Images/omnisage_ai_logo_transparent.png';
import { useAuth } from '../../contexts/AuthContext';

const menuItems = [
  {
    id: 'upload',
    label: 'Upload',
    icon: CloudUpload,
    description: 'Upload invoices',
    path: ['/', '/create-invoice'],
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    hoverBg: 'hover:bg-blue-100',
  },
  {
    id: 'invoices',
    label: 'Invoices',
    icon: Description,
    description: 'View and manage invoices',
    path: '/invoices',
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    hoverBg: 'hover:bg-green-100',
  },
  {
    id: 'tools',
    label: 'Tools',
    icon: Build,
    description: 'Business tools and utilities',
    path: '/tools',
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
    hoverBg: 'hover:bg-purple-100',
  },
  {
    id: 'config',
    label: 'Config',
    icon: Settings,
    description: 'Business Configurations',
    path: '/config',
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
    hoverBg: 'hover:bg-orange-100',
  },
];

export default function Sidebar() {
  const navigate = useNavigate();
  const location = useLocation();
  const { globSelectedBusiness } = useAuth();

  const handleNavigation = (path) => {
    if (Array.isArray(path)) {
      navigate(path[0]);
    } else {
      navigate(path);
    }
  };

  return (
    <div className="w-64 bg-white border-r border-gray-200 flex flex-col min-h-screen max-h-screen shadow-sm">
      {/* Business Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center justify-center">
          <img
            src={omniSageLetterLogo}
            alt="OmniSage AI"
            className="h-10 w-auto object-contain transition-transform duration-200 hover:scale-105"
          />
        </div>
      </div>

      {/* Selected Business Info */}
      <div className="p-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-gray-100">
        <SelectedBusinessInfo
          business_name={globSelectedBusiness.business_name}
          business_type={globSelectedBusiness.business_type}
          business_image={globSelectedBusiness.business_image}
          IsBusinessSelected={!!globSelectedBusiness}
        />
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 py-6 px-3 overflow-y-auto">
        <ul className="space-y-3">
          {menuItems.map((item, index) => {
            const Icon = item.icon;
            const isActive = Array.isArray(item.path)
              ? item.path.some((path) => location.pathname === path)
              : location.pathname === item.path;

            return (
              <li key={item.id} className="group">
                <button
                  onClick={() => handleNavigation(item.path)}
                  className={`w-full flex items-center space-x-4 p-3 rounded-xl text-left transition-all duration-300 select-none transform hover:scale-[1.02] hover:shadow-md ${
                    isActive
                      ? 'bg-gradient-to-r from-accent1 to-accent2 text-white shadow-lg border-l-4 border-white'
                      : `text-gray-700 ${item.hoverBg} hover:text-gray-900 border-l-4 border-transparent hover:border-gray-300`
                  }`}
                  style={{
                    animationDelay: `${index * 100}ms`,
                  }}
                >
                  <div className={`p-2 rounded-lg transition-all duration-300 ${
                    isActive
                      ? 'bg-white bg-opacity-20'
                      : `${item.bgColor} group-hover:scale-110`
                  }`}>
                    <Icon className={`w-5 h-5 transition-all duration-300 ${
                      isActive ? 'text-white' : item.color
                    }`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className={`font-semibold text-sm tracking-wide mb-0.5 transition-all duration-300 ${
                      isActive ? 'text-white' : 'text-gray-800'
                    }`}>
                      {item.label}
                    </p>
                    <p className={`text-xs leading-tight transition-all duration-300 ${
                      isActive ? 'text-white text-opacity-90' : 'text-gray-500'
                    }`}>
                      {item.description}
                    </p>
                  </div>
                  {isActive && (
                    <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                  )}
                </button>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-100 bg-gray-50">
        <div className="text-center">
          <p className="text-xs text-gray-500 font-medium">OmniSage AI</p>
          <p className="text-xs text-gray-400">v1.0.0</p>
        </div>
      </div>
    </div>
  );
}

function SelectedBusinessInfo({ IsBusinessSelected, business_name, business_type, business_image }) {
  if (!IsBusinessSelected) {
    return (
      <div className="text-center py-2">
        <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
          <Business className="w-6 h-6 text-gray-500" />
        </div>
        <div className="text-sm font-medium text-gray-600 mb-1">No business selected</div>
        <div className="text-xs text-gray-500 leading-relaxed">Please select a business to continue</div>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-3 p-2 rounded-lg bg-white bg-opacity-50 backdrop-blur-sm border border-white border-opacity-20">
      <div className="relative">
        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-accent1 to-accent2 flex items-center justify-center shadow-md">
          {business_image ? (
            <img
              src={business_image}
              alt="Business Logo"
              className="w-10 h-10 rounded-lg object-cover border-2 border-white"
            />
          ) : (
            <Business className="w-6 h-6 text-white" />
          )}
        </div>
        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
      </div>
      <div className="flex-1 min-w-0">
        <div className="text-sm font-semibold text-gray-900 truncate">{business_name}</div>
        {business_type && (
          <div className="text-xs text-gray-600 bg-gray-200 px-2 py-0.5 rounded-full inline-block mt-1">
            {business_type}
          </div>
        )}
      </div>
    </div>
  );
}
