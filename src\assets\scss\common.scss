@import './config.scss';

body {
  // background: url('../Images/BgLayer.svg') no-repeat #222021!important;
  background-size: cover;
  line-height: 1.2 !important;
  letter-spacing: 0.015em !important;
  font-family: $primaryFont !important;
  background-color: #f6f8fa;
  background-size: cover;
  color: $black;
  overflow-y: auto;
}

// ----- class selector style below, In order of newest last. -----

.dimmer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */
  z-index: 999; /* Ensure it appears above other elements */
}

.custom-dropdown .default.text {
  color: rgb(51, 51, 51) !important; /* Change placeholder text color */
}

.customDropdown {
  height: 3.5em;
  .dropdown {
    background-color: #f5f5f5 !important;
    color: $black !important;
    border: 1px solid #e9eaeb !important;
    border-radius: 10px !important;
    height: 100%;
    display: flex !important;
    align-items: center;
  }
}

.customDropdown5 {
  height: 3.5em;
  .default {
    color: #181d27 !important;
  }
  .dropdown {
    display: flex !important;
    align-items: center;
    justify-content: space-between !important;
    height: 100%;
    border-radius: 10px !important;
    color: $black !important;
    font-size: 1.1rem !important;
    border: 2px solid #fafafa !important;
    background-color: #fafafa !important;
    box-shadow: 0 0 1px 1px #e9eaeb !important;
    svg,
    path {
      fill: black;
    }
  }
}

.customDropdown4 {
  height: 3.5em;
  display: flex !important;
  border: 1px solid #e9eaeb !important;
  background-color: #f5f5f5 !important;
  border-radius: 10px !important;
  align-items: center;
  justify-content: space-between !important;
  .default {
    color: #181d27 !important;
  }
  .text {
    height: 75%;
    display: flex !important;
    align-items: center !important;
  }
  .dropdown {
    height: 100% !important;
  }
  svg,
  path {
    fill: black;
  }
}

.customDropdown2 {
  height: 3.5em;
  display: flex;
  border: 1px solid #e9eaeb !important;
  background-color: $white !important;
  border-radius: 10px !important;
  align-items: center;
  padding-left: 1em;
  .listingIcon {
    width: 25px !important;
    height: 25px !important;
  }
  .dropdown {
    display: flex !important;
    align-items: center !important;
    border-radius: 10px !important;
    color: $black !important;
    height: 100%;
    width: 100%;
    justify-content: space-between;
    border: none !important;
    .menu {
      left: -17% !important;
      width: 118% !important;
    }
  }
}

.customDropdown4withLoadMore {
  height: 3.5em;
  display: flex !important;
  border: 1px solid #e9eaeb !important;
  background-color: #f5f5f5 !important;
  border-radius: 10px !important;
  align-items: center;
  justify-content: space-between !important;
  position: relative;
  text-align: center; /* Ensure that the dropdown is the reference point for absolute positioning */

  .default {
    color: #181d27 !important;
  }

  .text {
    height: 75%;
    display: flex !important;
    align-items: center !important;
  }

  .dropdown {
    position: absolute;
    height: 100% !important;
  }

  svg,
  path {
    fill: black;
  }

  /* New styles for load more option */
  .load-more-option {
    // position: absolute;
    // bottom: -40px;
    // left: 50%;
    // transform: translateX(-50%);
    // background-color: #007bff;
    color: white;
    display: flex !important;
    justify-content: center !important;
    padding: 8px 16px;
    border-radius: 12px;
    text-align: center;
    z-index: 1000;
    cursor: pointer;
    transition: background-color 0.3s ease;

    span {
      width: 10em;
      height: 2.5em;
      background-color: #0056b3;
      color: white;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      font-size: 1.1em !important;
      border-radius: 5px !important;
    }

    &:hover {
      background-color: #0056b3;
    }
  }
}

// Ensure dropdown menu has enough space for load more button
.ui.dropdown .menu {
  position: absolute;
  margin-bottom: 50px;
}

.ui.checkbox label:before {
  border: 1px solid #8b8b8b;
}

.customTable {
  td {
    border: none !important;
    background-color: $white !important;
  }
  tr {
    td:first-child {
      border-top-left-radius: 10px; // Round top-left corner
      border-bottom-left-radius: 10px; // Round bottom-left corner
    }

    td:last-child {
      border-top-right-radius: 10px; // Round top-right corner
      border-bottom-right-radius: 10px; // Round bottom-right corner
    }
  }
}

.customCheckBox {
  label:before {
    border-color: #a4a7ae !important;
    border-width: 2px !important;
  }
  .checked {
    label:before {
      background-color: #4e5ba6 !important;
      border: none !important;
    }
  }
}

.customDropdown3 {
  padding: 0.5em;
  .default {
    color: #181d27 !important;
  }
  .menu {
    width: 100% !important;
    max-height: 15em !important;
    overflow-y: auto;
  }
}

.customModal.visible.transition {
  display: flex !important; /* Override block with flex */
  visibility: visible !important; /* Retain visibility */
  justify-content: flex-end !important; /* Align horizontally */
  align-items: flex-end !important; /* Align vertically to bottom */
}

.openStatus {
  background-color: #eaecf5 !important;
  color: #4e5ba6;
}

.pendingStatus {
  background-color: #fdf7dd !important;
  color: #7e6607;
}

.closedStatus {
  background-color: #f2d7d5 !important;
  color: #c0392b;
}

.verifiedStatus {
  background-color: #e3f8d9 !important;
  color: #2e7a31;
}

.deletedStatus {
  background-color: #fee4e2 !important;
  color: #b42318;
}

.completedStatus {
  background-color: #d5d5d569;
}

.duplicateStatus {
  background-color: #bdbdbd !important;
  color: #4a4949 !important;
}

.inProcessStatus {
  background-color: $accentColor2 !important;
  color: #ffffff;
}

.mainContainer {
  width: 100%;
  max-height: 100vh;
  max-height: 100dvh;
  overflow: hidden;
}

.hide {
  display: none !important;
}

.common-btn-schema {
  color: $primaryColor;
  background-color: $accentBgColor1;
  border: 1px solid $accentBorder1;
  transition-property: all;
  transition-duration: 200ms;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;

  &:hover {
    border: 1px solid $accentColor1;
    background-color: rgba($accentBgColor1, 0.2);
  }
}

/* Custom toast container style (react-toastify) */
.Toastify__toast {
  @include for_media(mobileScreen) {
    width: 80vw !important;
    max-width: 80vw !important;
    margin: 0 auto;
  }
}

// ----- id selector style below, In order of newest last -----

#tooltip,
:global(.react-tooltip) {
  background-color: $accentColor2;
  border-radius: 15px;
  z-index: 1000 !important;
  width: fit-content;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  text-align: center;
  word-wrap: break-word;
  @include for_media(mobileScreen) {
    width: 80vw;
    width: 80dvw;
  }
}

.blankSkeleton {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  background: linear-gradient(90deg, #eaeaea 25%, #f5f5f5 50%, #eaeaea 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// ----- mixin below, In order of newest last -----

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin clickable {
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
}

@mixin hide-scrollbar {
  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;
  -ms-overflow-style: none;
}
