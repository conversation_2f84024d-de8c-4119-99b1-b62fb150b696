import React, { useState } from 'react';
import { Autocomplete, TextField, CircularProgress } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import apiClient from '../services/apiClient';
import { useAuth } from '../../contexts/AuthContext';
import useDebounce from '../global/hooks/useDebounce';

export default function SearchAutocomplete({
  url,
  searchParam = 'search',
  onSelect,
  transformOptions = { key: 'id', label: 'name', value: 'id' },
  ...props
}) {
  const { globSelectedBusiness } = useAuth();
  const [inputValue, setInputValue] = useState('');
  const debounceValue = useDebounce(inputValue, 500);

  const { data: options = [], isLoading } = useQuery({
    queryKey: ['search-autocomplete', url, debounceValue],
    queryFn: async () => {
      const res = await apiClient.get(url, {
        params: { [searchParam]: debounceValue },
      });

      const raw = Array.isArray(res.data) ? res.data : res.results || [];

      return raw.map((item) => ({
        key: item[transformOptions.key] ?? null,
        label: item[transformOptions.label] ?? '',
        value: item[transformOptions.value] ?? null,
      }));
    },
    enabled: !!globSelectedBusiness?.business_id,
    staleTime: 5 * 60 * 1000,
    keepPreviousData: true,
  });

  return (
    <Autocomplete
      options={options}
      getOptionLabel={(option) => (typeof option === 'string' ? option : option.label || '')}
      loading={isLoading}
      autoHighlight
      openOnFocus
      noOptionsText="No options available"
      onInputChange={(e, value) => setInputValue(value)}
      onChange={(e, value) => onSelect?.(value)}
      renderInput={(params) => (
        <TextField
          {...params}
          {...props}
          slotProps={{
            inputAdornment: {
              endAdornment: (
                <>
                  {isLoading && <CircularProgress color="inherit" size={20} />}
                  {params.InputProps?.endAdornment}
                </>
              ),
            },
          }}
        />
      )}
    />
  );
}
