import React from 'react';
import ModalDropdown from '../ModalDropdown';
import <PERSON><PERSON><PERSON> from '../AiField';
import { getFieldAlertObject } from '../../../../../utils/aiUtils';
import { NumericFormat } from 'react-number-format';

function ZohoGstFields({ data, isReadOnly, index, gstLedgersUrl, formAction, SECTION, isSameState }) {
  if (isSameState) {
    return (
      <>
        <AiField
          label="CGST+SGST"
          isExactMatch={data?.exact_match?.sgst_amount && data?.exact_match?.cgst_amount}
          alertObject={getFieldAlertObject(data, 'sgst_amount')}
          name="cgst_sgst_amount"
          id={`cgst_sgst_amount_${index}`}
          disabled={isReadOnly}
          readOnly
          alignSuggestionRight={true}
          renderCustomField={() => (
            <NumericFormat
              value={Number(data?.sgst_amount) + Number(data?.cgst_amount) ?? ''}
              thousandSeparator={true}
              thousandsGroupStyle="lakh"
              decimalScale={2}
              fixedDecimalScale
              allowNegative={true}
              className="input-field text-right"
              readOnly
              disabled={isReadOnly}
              id={`cgst_sgst_amount_${index}`}
            />
          )}
        />

        <div>
          <AiField
            label="Ledger"
            alertObject={getFieldAlertObject(data, 'zoho_tax_group_ledger_name')}
            type="text"
            value={data?.zoho_tax_group_ledger_name ?? ''}
            readOnly
            disabled={isReadOnly}
            name="zoho_tax_group_ledger_name"
            id="zoho_tax_group_ledger_name"
            required={true}
          />
          <ModalDropdown
            label="Select Ledger"
            onSelect={(option) =>
              formAction(
                'INDEX_UPDATE_SECTION',
                SECTION,
                null,
                {
                  zoho_tax_group_ledger_name: option.label,
                  zoho_tax_group_ledger_id: String(option.key),
                },
                index
              )
            }
            url={`${gstLedgersUrl}&gst_type=tax_group`}
            disabled={isReadOnly}
            transformOptionsObj={{
              key: 'master_id',
              value: 'master_id',
              label: 'ledger',
            }}
          />
        </div>
      </>
    );
  }
  // Default IGST Amount and Ledger
  return (
    <>
      {/* IGST Amount */}
      <AiField
        label="IGST Amount"
        isExactMatch={data?.exact_match?.igst_amount}
        alertObject={getFieldAlertObject(data, 'igst_amount')}
        name="igst_amount"
        id={`igst_amount_${index}`}
        disabled={isReadOnly}
        alignSuggestionRight={true}
        readOnly
        renderCustomField={() => (
          <NumericFormat
            value={data?.igst_amount ?? ''}
            thousandSeparator={true}
            thousandsGroupStyle="lakh"
            decimalScale={2}
            fixedDecimalScale
            allowNegative={true}
            className="input-field text-right"
            disabled={isReadOnly}
            readOnly
            id={`igst_amount_${index}`}
          />
        )}
      />

      {/* IGST Ledger */}
      <div>
        <AiField
          label="Ledger"
          alertObject={getFieldAlertObject(data, 'zoho_tax_ledger_name')}
          type="text"
          value={data?.zoho_tax_ledger_name ?? ''}
          readOnly
          disabled={isReadOnly}
          name="zoho_tax_ledger_name"
          id={`zoho_tax_ledger_name${index}`}
          required={true}
        />
        <ModalDropdown
          label="Select Ledger"
          onSelect={(option) =>
            formAction(
              'INDEX_UPDATE_SECTION',
              SECTION,
              null,
              {
                zoho_tax_ledger_name: option.label,
                zoho_tax_ledger_id: String(option.key),
              },
              index
            )
          }
          url={`${gstLedgersUrl}&gst_type=tax`}
          disabled={isReadOnly}
          transformOptionsObj={{
            key: 'master_id',
            value: 'master_id',
            label: 'ledger',
          }}
        />
      </div>
    </>
  );
}

export default ZohoGstFields;
