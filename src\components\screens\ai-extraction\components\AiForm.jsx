import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import './AiForm.scss';
import { Eye, PencilLine, X, AlertCircle, BookCopy, ArrowRight, ArrowLeft } from 'lucide-react';
import { debounce } from 'lodash';
import { validateRequiredFields } from './validation';
import { mapAiInvoiceStatus } from '../../../utils/aiUtils';
import { getStatusClass } from '../../../utils';
import {
  SupplierDetailsSection,
  BillToDetailsSection,
  ShipToDetailsSection,
  OtherInformationSection,
  ProductServicesSection,
  InvoiceSummarySection,
  TransportDetailsSection,
  AdditionalInformationSection,
  GstSummarySection,
} from '../form-sections';
import { useParams } from 'react-router-dom';
import {
  getOriginalInvoice,
  invoiceComment,
  saveDraft,
  submitExtraction,
  syncZoho,
} from '../../../services/aiServices';
import { createLoadingToast } from '../../../utils/toastUtils';
import { getErrorMessage } from '../../../utils/apiUtils';
import SelectOption from '../../../ui-components/SelectOption';
import usePrompt from '../../../global/hooks/usePrompt';
import { removeObjectsFromJson } from '../../../utils/jsonUtils';
import ticketServices from '../../../services/ticketServices';
import { toast } from 'react-toastify';
import { getSectionsWithErrors } from '../../../utils/aiUtils';
import DuplicateWarning from './DuplicateWarning';
import DuplicateAlertModal from './DuplicateAlertModal';
import ExpandableSection from './ExpandableSection';
import { Tooltip } from 'react-tooltip';
import Checkbox from '../../../ui-components/fields/Checkbox';
import ZohoSubmitBtn from '../form-sections/components/zoho/ZohoSubmitBtn';
import ValidationTooltip from './ValidationTooltip';
import TallySubmitBtn from '../form-sections/components/tally/TallySubmitBtn';

const SECTIONS = [
  {
    title: 'Supplier Details',
    component: SupplierDetailsSection,
    jsonKey: 'supplier_details',
  },
  {
    title: 'Bill To Details',
    component: BillToDetailsSection,
    jsonKey: 'bill_to_details',
  },
  {
    title: 'Ship To Details',
    component: ShipToDetailsSection,
    jsonKey: 'ship_to_details',
  },
  {
    title: 'Transport Details',
    component: TransportDetailsSection,
    jsonKey: 'transport_details',
  },
  {
    title: 'Sales of Product / Services details',
    component: ProductServicesSection,
    jsonKey: 'sales_of_product_services',
  },
  {
    title: 'GST Summary',
    component: GstSummarySection,
    jsonKey: 'gst_ledgers',
  },
  {
    title: 'Invoice Summary',
    component: InvoiceSummarySection,
    jsonKey: 'invoice_summary',
  },
  {
    title: 'Other Information',
    component: OtherInformationSection,
    jsonKey: 'other_information',
  },
  {
    title: 'Additional Information',
    component: AdditionalInformationSection,
    jsonKey: 'additional_information',
  },
];

const sections_json_keys = SECTIONS.map((section) => section.jsonKey);

function AiForm({
  categoryOptions,
  subCategoryOptions,
  extractedData,
  businessId,
  refetch,
  handlePrevious,
  handleNext,
  reachedStatus,
  didNotFound,
}) {
  const { fileId } = useParams();
  const { prompt, PromptModal } = usePrompt();
  const isReadOnlyInitial = useMemo(
    () =>
      extractedData?.status === '3' ||
      extractedData?.status === '4' ||
      extractedData?.status === '5' ||
      extractedData?.status === '6' ||
      extractedData?.status === '7' ||
      extractedData?.status === '8',
    [extractedData]
  );
  const [formData, setFormData] = useState(extractedData || {});
  const originalInvoice = useRef(null);
  const [showOriginal, setShowOriginal] = useState(false);
  const [expandedSections, setExpandedSections] = useState(new Set(sections_json_keys));
  const [invoiceType, setInvoiceType] = useState(extractedData?.invoice_type || null);
  const [isByPassValidation, setIsBypassValidation] = useState(false);
  const [isReadOnly, setIsReadOnly] = useState(isReadOnlyInitial);
  const [duplicateInvoicesObj, setDuplicateInvoicesObj] = useState(null);
  const [sectionsWithErrors, setSectionsWithErrors] = useState([]);
  const [sectionsWithMissingFields, setSectionsWithMissingFields] = useState(
    [null] // adding null to avoid Validate button from being enabled for the first time
  );

  useEffect(() => {
    setIsReadOnly(isReadOnlyInitial);
  }, [isReadOnlyInitial]);

  useEffect(() => {
    if (invoiceType) {
      setExpandedSections(new Set(sections_json_keys));
    }
  }, [invoiceType]);

  const debouncedValidation = useCallback(
    debounce((newFormData) => {
      const errorSections = getSectionsWithErrors(newFormData, ['duplicate_validation']);
      setSectionsWithErrors(errorSections);

      const missingSections = validateRequiredFields(newFormData);
      setSectionsWithMissingFields(missingSections);
    }, 400),
    []
  );

  // Update validation when formData changes
  useEffect(() => {
    if (formData) {
      debouncedValidation(formData);
    }
  }, [formData, debouncedValidation]);

  /**
   * This function handles various form actions based on the action type
   * @param {string} action - The action type to perform (e.g., UPDATE_SECTION, HARD_UPDATE_SECTION)
   * @param {string} section - The form section key
   * @param {string} field - The form section field's key
   * @param {string|number|object} value - New value or update object
   */
  const formAction = useCallback((action, section, field, value, index) => {
    let updatedSection;

    setFormData((prevFormData) => {
      const prevSection = prevFormData[section] || {};
      switch (action) {
        case 'FIELD_CHANGE':
          updatedSection = { ...prevSection, [field]: value };
          break;

        case 'INDEX_FIELD_CHANGE':
          updatedSection = [...prevSection];
          updatedSection[index][field] = value;
          break;

        case 'UPDATE_SECTION':
          updatedSection = { ...prevSection, ...value };
          break;

        case 'INDEX_UPDATE_SECTION':
          updatedSection = [...prevSection];
          updatedSection[index] = { ...prevSection[index], ...value };
          break;

        case 'HARD_UPDATE_SECTION':
          updatedSection = value;
          break;

        default:
          return prevFormData;
      }
      return {
        ...prevFormData,
        [section]: updatedSection,
      };
    });

    return updatedSection;
  }, []);

  const countSectionAlert = (section, key) => {
    const dataToUse = showOriginal ? originalInvoice.current : formData;
    const sectionData = dataToUse[section];
    if (!sectionData) return 0;
    if (Array.isArray(sectionData)) {
      return sectionData.reduce((count, item) => {
        const alerts = item?.[key];
        return count + (alerts ? Object.keys(alerts).length : 0);
      }, 0);
    }
    const alerts = sectionData?.[key];
    return alerts ? Object.keys(alerts).length : 0;
  };

  const handleSaveDraft = useCallback(() => {
    const finalFormData = removeObjectsFromJson(formData, ['error', 'warning', 'recommended_fields', 'exact_match']);
    const loadingToast = createLoadingToast('Saving draft...');
    saveDraft(fileId, businessId, finalFormData)
      .then(() => {
        loadingToast('success', 'Draft saved successfully');
      })
      .catch((err) => {
        const errorMessage = getErrorMessage(err);
        loadingToast('error', errorMessage);
      });
  }, [formData, fileId]);

  const handleSync = useCallback(
    ({ formDataProp = null, isShowSuccessToast = false, isShowValidateAndSync = false }) => {
      let finalFormData;
      if (!formDataProp) {
        const parsedFormData =
          removeObjectsFromJson(formData, ['error', 'warning', 'recommended_fields', 'exact_match']) || [];

        finalFormData = {
          ...parsedFormData,
          is_by_pass_validation: isByPassValidation,
        };
      } else {
        finalFormData = formDataProp;
      }
      syncZoho(businessId, finalFormData)
        .then(() => {
          if (isShowSuccessToast) {
            toast.success('Synced successfully');
          }
          if (isShowValidateAndSync) {
            toast.success('Validation and Synced successfully');
          }
        })
        .catch((err) => {
          const errorMessage = getErrorMessage(err);
          toast.error(errorMessage);
        })
        .finally(() => {
          refetch(false);
        });
    },
    [formData, businessId, isByPassValidation]
  );

  const handleSubmit = useCallback(
    async (isForceBypassValidation = false) => {
      let finalFormData;
      const finalBypassValidation = isForceBypassValidation ? isForceBypassValidation : isByPassValidation;
      const parsedFormData =
        removeObjectsFromJson(formData, ['error', 'warning', 'recommended_fields', 'exact_match']) || [];

      finalFormData = {
        ...parsedFormData,
        is_by_pass_validation: finalBypassValidation,
      };

      const loadingToast = createLoadingToast('Validating extraction...');

      submitExtraction(fileId, businessId, finalFormData)
        .then((res) => {
          const errorMessage = res?.message || 'Failed to submit extraction';
          if (res?.status === 'success') {
            setIsReadOnly(true);
            setIsBypassValidation(false);
            if (
              extractedData?.enable_auto_sync_invoice &&
              extractedData?.accounting_platform?.toLowerCase() === 'zoho'
            ) {
              handleSync({
                formDataProp: finalFormData,
                refetch: true,
                isShowValidateAndSync: true,
              });
              loadingToast('dismiss');
            } else {
              loadingToast('success', 'Extraction submitted successfully');
              refetch();
            }
          } else {
            if (res?.duplicate_validation) {
              setDuplicateInvoicesObj(res?.duplicate_validation);
              loadingToast('dismiss');
            } else {
              loadingToast('error', errorMessage);
            }
            const sectionThatHasError = getSectionsWithErrors(res);
            setSectionsWithErrors(sectionThatHasError);
            setFormData(res);
          }
        })
        .catch((err) => {
          const errorMessage = getErrorMessage(err);
          loadingToast('error', errorMessage);
        });
    },
    [formData, fileId, businessId, isByPassValidation, isReadOnlyInitial]
  );

  const handleCategoryChange = useCallback(
    (selectedOption) => {
      if (!selectedOption || selectedOption.value === invoiceType) return;
      setInvoiceType(selectedOption.value);
      ticketServices
        .updateTicket(extractedData?.ticket_id, {
          category: selectedOption.value,
          sub_category: subCategoryOptions?.value,
        })
        .then(() => {
          toast.success('Category updated successfully');
          setExpandedSections(new Set(sections_json_keys));
          refetch(false);
        })
        .catch((err) => {
          setInvoiceType(invoiceType);
          const errorMessage = getErrorMessage(err);
          toast.error(errorMessage);
        });
    },
    [fileId, invoiceType, extractedData?.ticket_id]
  );

  const handleEdit = useCallback(async () => {
    let editComment;
    editComment = await prompt('Do you want to edit the invoice?', 'Please enter your reason', 20);
    if (editComment) {
      const payload = {
        message_type: 're_edit',
        user_comment: editComment,
      };
      invoiceComment(fileId, businessId, payload)
        .then(() => {
          toast.success('Invoice is now editable');
          refetch();
        })
        .catch((err) => {
          const error = getErrorMessage(err);
          toast.error(error);
        });
    }
  }, []);

  const handleDuplicate = useCallback(
    async (duplicateType, comment = '') => {
      switch (duplicateType) {
        case 'MarkAsDuplicate':
          const duplicateJson = JSON.stringify(duplicateInvoicesObj || extractedData?.duplicate_validation);
          const payload = {
            message_type: 'duplicate',
            system_message: duplicateJson,
          };
          invoiceComment(fileId, businessId, payload)
            .then(() => {
              setDuplicateInvoicesObj(null);
              toast.success('Marked Invoice as Duplicate');
              refetch();
            })
            .catch((err) => {
              const error = getErrorMessage(err);
              toast.error(error);
            });
          return;

        case 'MarkAsNotDuplicate':
          const notDuplicatePayload = {
            user_comment: comment,
            message_type: 'duplicate_override',
          };
          invoiceComment(fileId, businessId, notDuplicatePayload)
            .then(() => {
              setDuplicateInvoicesObj(null);
              handleSubmit(true);
            })
            .catch((err) => {
              const error = getErrorMessage(err);
              toast.error(error);
            });
          return;

        case 'MarkAsDuplicateWithComment':
          let reason;
          reason = await prompt('Do you want to mark this invoice as duplicate?', 'Please enter your reason', 20);
          if (reason) {
            invoiceComment(fileId, businessId, { message_type: 'duplicate' })
              .then(() => {
                toast.success('Marked Invoice as Duplicate');
                refetch();
              })
              .catch((err) => {
                const error = getErrorMessage(err);
                toast.error(error);
              });
          }
          return;
        default:
          return;
      }
    },
    [extractedData?.duplicate_validation, duplicateInvoicesObj]
  );

  const handleViewOriginal = useCallback(() => {
    if (originalInvoice.current) {
      setShowOriginal(!showOriginal);
    } else {
      getOriginalInvoice(fileId, businessId)
        .then((res) => {
          originalInvoice.current = res;
          setShowOriginal(true);
        })
        .catch((err) => {
          const error = getErrorMessage(err);
          toast.error(error);
        });
    }
  }, [showOriginal]);

  const handleBypassValidation = useCallback(async () => {
    if (!isByPassValidation) {
      const bypassComment = await prompt('Do you want to Bypass Validation?', 'Please enter your reason', 20);
      if (bypassComment) {
        const payload = {
          message_type: 'validation_override',
          user_comment: bypassComment,
        };
        invoiceComment(fileId, businessId, payload)
          .then((res) => {
            toast.success(res?.message);
            setIsBypassValidation(true);
          })
          .catch((err) => {
            const error = getErrorMessage(err);
            toast.error(error);
          });
      }
    } else {
      setIsBypassValidation(false);
    }
  }, [isByPassValidation]);

  return (
    <div id="extraction-form" className="extraction-form">
      <div id="form-header" className="form-header">
        <div className="flex items-center flex-wrap gap-4 mr-auto">
          <SelectOption
            label="Category"
            options={categoryOptions}
            defaultValue={invoiceType}
            onChange={handleCategoryChange}
            placeholder="Select Invoice Type"
            disabled={isReadOnlyInitial}
          />
          <SelectOption label="Sub-category" disabled={true} placeholder="Invoice" className="min-w-11" />
          {extractedData?.status && (
            <div className="flex flex-col">
              <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <div
                className={`w-fit min-w-11 flex items-center justify-center select-none rounded-full py-1 px-2 text-base ${getStatusClass(
                  extractedData?.status,
                  true
                )}`}
              >
                {mapAiInvoiceStatus(extractedData?.status)}
              </div>
            </div>
          )}
        </div>
        <div className="flex items-center gap-2 ml-auto">
          {isReadOnly && !showOriginal && (
            <button type="button" className="btn" onClick={handleEdit}>
              <PencilLine size={20} strokeWidth={2} />
              <span>Edit</span>
            </button>
          )}
          {!isReadOnly && (
            <button
              type="button"
              className="btn"
              onClick={() =>
                handleDuplicate(extractedData?.duplicate_validation ? 'MarkAsDuplicate' : 'MarkAsDuplicateWithComment')
              }
            >
              <BookCopy size={20} strokeWidth={2} />
              <span>Mark As Duplicate</span>
            </button>
          )}
          <button type="button" className={`btn ${showOriginal ? 'active' : ''}`} onClick={handleViewOriginal}>
            <Eye size={20} strokeWidth={2} />
            <span>View Original</span>
          </button>
        </div>
      </div>
      {showOriginal && <ViewOriginalBanner showOriginal={showOriginal} setShowOriginal={setShowOriginal} />}
      {/* Duplicate Validation Warning */}
      {extractedData?.duplicate_validation && (
        <DuplicateWarning
          duplicateData={extractedData?.duplicate_validation?.duplicate_invoices || []}
          duplicateType={extractedData?.duplicate_validation?.type || 'possible'}
        />
      )}

      <div className="sections-container">
        {SECTIONS.map(({ title, component: Component, jsonKey }) => {
          const errorCount = countSectionAlert(jsonKey, 'error');
          return (
            <div key={jsonKey}>
              <ExpandableSection
                key={jsonKey}
                title={title}
                expandedSections={expandedSections}
                setExpandedSections={setExpandedSections}
                sectionId={jsonKey}
                errorCount={errorCount}
                warningCount={countSectionAlert(jsonKey, 'warning')}
                hasMissingFields={sectionsWithMissingFields.includes(jsonKey)}
                invoiceType={true}
              >
                <Component
                  formData={showOriginal ? originalInvoice.current : formData}
                  errorCount={errorCount}
                  isReadOnly={isReadOnly || showOriginal}
                  invoiceType={invoiceType}
                  setFormData={setFormData}
                  formAction={formAction}
                />
              </ExpandableSection>
            </div>
          );
        })}
      </div>

      {
        <div id="form-footer" className="form-footer">
          <div className="buttons-wrapper">
            <button
              type="button"
              className="btn"
              onClick={handlePrevious}
              disabled={reachedStatus.reachedTop || didNotFound}
              data-tooltip-id="tooltip-nav-btns"
              data-tooltip-content={
                didNotFound
                  ? 'Invoice not found'
                  : reachedStatus.reachedTop
                    ? 'You are on the latest invoice'
                    : 'Go to previous invoice'
              }
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            {!isReadOnly && !showOriginal && (
              <button type="button" className="btn-draft" onClick={handleSaveDraft}>
                <span>Save Draft</span>
              </button>
            )}

            <div className="flex items-center gap-3 ml-auto">
              {!isReadOnlyInitial && (
                <Checkbox label="Bypass Validation" checked={isByPassValidation} onChange={handleBypassValidation} />
              )}
              {extractedData?.accounting_platform?.toLowerCase() === 'zoho' ? (
                <ZohoSubmitBtn
                  enable_auto_sync={extractedData?.enable_auto_sync_invoice}
                  handleSubmit={handleSubmit}
                  isDisabled={
                    (sectionsWithErrors.length > 0 || sectionsWithMissingFields.length > 0 || !invoiceType) &&
                    !isByPassValidation
                  }
                  status={extractedData?.status}
                  handleSync={handleSync}
                />
              ) : (
                <TallySubmitBtn
                  handleSubmit={handleSubmit}
                  isDisabled={
                    (sectionsWithErrors.length > 0 || sectionsWithMissingFields.length > 0 || !invoiceType) &&
                    !isByPassValidation
                  }
                  status={extractedData?.status}
                />
              )}
            </div>

            <button
              type="button"
              className="btn"
              onClick={handleNext}
              disabled={reachedStatus.reachedEnd || didNotFound}
              data-tooltip-id="tooltip-nav-btns"
              data-tooltip-content={
                didNotFound
                  ? 'Invoice not found'
                  : reachedStatus.reachedEnd
                    ? 'You are on the oldest invoice'
                    : 'Go to next invoice'
              }
            >
              <ArrowRight className="w-5 h-5" />
            </button>
          </div>
        </div>
      }

      <DuplicateAlertModal
        isOpen={!!duplicateInvoicesObj}
        duplicateData={duplicateInvoicesObj?.duplicate_invoices || []}
        duplicateType={duplicateInvoicesObj?.type || 'possible'}
        onMarkAsDuplicate={() => handleDuplicate('MarkAsDuplicate')}
        onClose={() => setDuplicateInvoicesObj(null)}
        onMarkAsNotDuplicate={(comment) => handleDuplicate('MarkAsNotDuplicate', comment)}
      />
      {(sectionsWithErrors.length > 0 || sectionsWithMissingFields.length > 0) &&
        !isByPassValidation &&
        extractedData?.status !== '3' && (
          <ValidationTooltip
            sectionsWithErrors={sectionsWithErrors}
            sectionsWithMissingFields={sectionsWithMissingFields}
            transformSectionName={{
              gst_ledgers: 'GST Summary',
            }}
          />
        )}
      <PromptModal />

      {/* Using this instead global `tooltip` because this required to be rebuilt when this component mounts*/}
      <Tooltip id="tooltip-nav-btns" />
    </div>
  );
}

function ViewOriginalBanner({ showOriginal, setShowOriginal }) {
  return (
    <div className="view-original-banner">
      <div className="flex items-center gap-2">
        <AlertCircle size={20} />
        <span>You are viewing the original data. Form is in read-only mode.</span>
      </div>
      <button
        className="cursor-pointer bg-transparent border-none text-[#0B1A30] p-1 rounded-full hover:bg-black/5"
        onClick={() => setShowOriginal(!showOriginal)}
      >
        <X size={18} />
      </button>
    </div>
  );
}

export default AiForm;
